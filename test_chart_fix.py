#!/usr/bin/env python3
"""
Test script to verify the TerraFinChart show() method works correctly.
This test creates a simple chart and displays it.
"""

import sys
import os

# Add the TerraFin package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'TerraFin', 'src'))

try:
    from TerraFin.visualization.lightweight_chart import TerraFinChart
    import pandas as pd
    import numpy as np
    
    def test_chart_show():
        """Test that the chart can be created and shown without errors."""
        print("Creating TerraFinChart...")
        
        # Create a simple chart
        chart = TerraFinChart(
            width=800,
            height=600,
            title="Test Chart",
            debug=True
        )
        
        print("Chart created successfully!")
        
        # Create some sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        df = pd.DataFrame({
            'time': dates,
            'open': prices,
            'high': prices + np.random.rand(100) * 2,
            'low': prices - np.random.rand(100) * 2,
            'close': prices + np.random.randn(100) * 0.5
        })
        
        print("Sample data created")
        
        # Set the data
        chart.set(df)
        print("Data set on chart")
        
        # Test the show method (non-blocking)
        print("Testing show() method...")
        chart.show(block=False)
        print("show() method completed successfully!")
        
        # Clean up
        chart.exit()
        print("Chart exited successfully!")
        
        return True
        
    if __name__ == "__main__":
        try:
            success = test_chart_show()
            if success:
                print("\n✅ Test passed! The chart show() method works correctly.")
            else:
                print("\n❌ Test failed!")
                sys.exit(1)
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure the lightweight_charts package is installed and TerraFin is in the correct location.")
    sys.exit(1)
